<?php

namespace Tests\Unit\Domains\Subscription\Application;

use App\Actions\Subscription\RequestSubscriptionCancellationAction;
use App\Actions\Subscription\RevokeSubscriptionCancellationAction;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\SubscriptionCancellation;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionCancellationTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_request_cancellation_with_valid_subscription(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $cancellation = app(RequestSubscriptionCancellationAction::class)
            ->execute($pharmacy, $owner, 'Test reason');

        $this->assertInstanceOf(SubscriptionCancellation::class, $cancellation);

        $this->assertDatabaseHas('subscription_cancellations', [
            'pharmacy_id' => $pharmacy->id,
            'requestable_id' => $owner->id,
            'status' => SubscriptionCancellationStatusEnum::CONFIRMED,
            'executed_at' => null,
        ]);
    }

    public function test_cannot_request_cancellation_without_subscription(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $action = app(RequestSubscriptionCancellationAction::class);

        $this->expectException(SubscriptionCancellationException::class);

        $action->execute($pharmacy, $owner);
    }

    public function test_cannot_request_cancellation_with_existing_active_cancellation(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        SubscriptionCancellation::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => SubscriptionCancellationStatusEnum::CONFIRMED,
        ]);

        $action = app(RequestSubscriptionCancellationAction::class);

        $this->expectException(SubscriptionCancellationException::class);

        $action->execute($pharmacy, $owner);
    }

    public function test_user_cannot_revoke_cancellation(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $cancellation = SubscriptionCancellation::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => SubscriptionCancellationStatusEnum::CONFIRMED,
        ]);

        $action = app(RevokeSubscriptionCancellationAction::class);
        $this->expectException(SubscriptionCancellationException::class);

        $action->execute($pharmacy, $owner);
    }

    public function test_calculates_next_quarter_end_correctly(): void
    {
        /* start subscription, requested_at, expected */
        $testCases = [
            ['2025-01-01', '2025-01-15', '2025-06-30'], // 1. Fall: 3 Monate nach Start -> Ende Q2
            ['2025-01-01', '2025-03-31', '2025-06-30'], // 2. Fall: Ende Q1 -> Ende Q2
            ['2025-01-01', '2025-07-01', '2025-09-30'], // 3. Fall: Anfang Q3 -> Ende Q3
            ['2025-01-01', '2025-12-15', '2026-03-31'], // 4. Fall: Mitte Q4 -> Ende Q1 nächstes Jahr
        ];

        foreach ($testCases as [$startSubscription, $requestedAt, $expected]) {
            $startSubscriptionDate = Carbon::parse($startSubscription);
            $requestedAtDate = Carbon::parse($requestedAt);
            $expectedDate = Carbon::parse($expected);
            $this->travelTo($startSubscriptionDate);

            [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
            $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
            $this->travelTo($requestedAtDate);

            $resultDate = $pharmacy->calculateNextQuarterEnd();

            $this->assertEquals(
                $expectedDate->toDateString(),
                $resultDate->toDateString(),
            );
        }
    }

    public function test_pharmacy_has_active_cancellation_method(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        // Keine Kündigung
        $this->assertFalse($pharmacy->hasActiveCancellation());

        // Ausstehende Kündigung
        SubscriptionCancellation::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => SubscriptionCancellationStatusEnum::PENDING,
        ]);
        $pharmacy->refresh();
        $this->assertTrue($pharmacy->hasActiveCancellation());

        // Widerrufene Kündigung
        $pharmacy->subscriptionCancellation->update([
            'status' => SubscriptionCancellationStatusEnum::REVOKED,
        ]);
        $pharmacy->refresh();
        $this->assertFalse($pharmacy->hasActiveCancellation());
    }

    private function mockSubscription($pharmacy, $productClass): void
    {
        // Mock für isSubscribedToProduct
        $pharmacy->shouldReceive('isSubscribedToProduct')
            ->with($productClass)
            ->andReturn(true);

        $pharmacy->shouldReceive('subscription')
            ->andReturn((object) ['created_at' => now()->subMonths(4)]);
    }
}
