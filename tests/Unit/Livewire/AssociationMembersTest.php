<?php

namespace Tests\Unit\Livewire;

use App\Association;
use App\AssociationMembershipChange;
use App\Domains\Association\Domain\Enums\MemberImportStatus;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Enums\AssociationRoleEnum;
use App\Enums\SalutationEnum;
use App\Livewire\Association\Components\AssociationMembers;
use App\MemberImport;
use App\User;
use App\UserPharmacyProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\FileUploadConfiguration;
use Livewire\Livewire;
use PhpOffice\PhpSpreadsheet\Calculation\Calculation;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

/**
 * @group AssociationMembers
 */
class AssociationMembersTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public $emailCounter = 13;

    public function createMemberImport(User $user, MemberImportStatus $status, ?string $statusDescription = null): MemberImport
    {
        $memberImport = new MemberImport([
            'title' => $user->title,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'data' => $user->toArray(),
            'status' => $status,
            'status_description' => $statusDescription,
        ]);

        $memberImport->forceFill([
            'created_at' => $user->created_at,
            'updated_at' => $user->created_at,
        ]);

        $memberImport->association()->associate($user->association()->getKey());

        return $memberImport;
    }

    public function getRemoveAssociationMemberModal(AssociationMembers $livewire): null
    {
        return $livewire->openRemoveAssociationMemberModal();
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpFaker();
    }

    public function test_it_renders_the_association_members_component(): void
    {
        [$associationAdmin] = $this->createAssociationAdminOwnerAndPharmacy();

        $component = Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->assertViewIs('livewire.association.components.members');

        $this->assertNotNull($component->members);
    }

    public function test_it_renders_the_association_members_component_with_members(): void
    {
        $association = Association::factory()->create();

        UserPharmacyProfile::factory()
            ->count(3)
            ->for($association)
            ->has(User::factory())
            ->create();

        $component = Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->assertViewIs('livewire.association.components.members');

        $this->assertNotNull($component->members);
    }

    public function test_it_validates_file_upload(): void
    {
        $association = Association::factory()->create();

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->set('file', 'not-an-uploaded-file')
            ->call('addMembers')
            ->assertHasErrors(['file']);
    }

    public function test_it_does_not_close_modal_on_error(): void
    {
        $association = Association::factory()->create();

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->set('file', 'not-an-uploaded-file')
            ->call('addMembers')
            ->assertHasErrors(['file'])
            ->assertSet('modalOpen', true);
    }

    #[DataProvider('allowed_file_type_provider')]
    public function test_it_adds_members_from_accepted_file_types(string $extension): void
    {
        Storage::fake(FileUploadConfiguration::disk());
        Queue::fake();

        $association = Association::factory()->create();
        $fileName = 'association_members_test_excel'.$extension;
        $filePath = $this->getTestExcelPath($fileName);

        $this->assertFileExists($filePath);

        $file = UploadedFile::fake()->createWithContent($fileName, file_get_contents(base_path($filePath)));

        $this->assertFileEquals($filePath, $file);

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->set('file', $file)
            ->call('addMembers')
            ->assertHasNoErrors()
            ->assertSet('modalOpen', false)
            ->assertDispatched('close-modal');

        unlink($filePath);
    }

    public function test_it_adds_errors_not_accepted_file_types(): void
    {
        Storage::fake(FileUploadConfiguration::disk());

        $association = Association::factory()->create();
        $fileName = 'association_members_test_excel.csv';
        $filePath = $this->getTestExcelPath($fileName);
        $file = UploadedFile::fake()->createWithContent($fileName, file_get_contents(base_path($filePath)));

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->set('file', $file)
            ->call('addMembers')
            ->assertHasErrors()
            ->assertSet('modalOpen', true)
            ->assertNotDispatched('close-modal');

        unlink($filePath);
    }

    public static function allowed_file_type_provider(): array
    {
        return [
            ['.xls'],
            ['.xlsx'],
        ];
    }

    public function getTestExcelPath(string $fileName): string
    {
        $filePath = 'tests'.DIRECTORY_SEPARATOR.'storage';
        $file = $filePath.DIRECTORY_SEPARATOR.$fileName;
        $spreadsheet = new Spreadsheet;
        Calculation::getInstance($spreadsheet)->disableCalculationCache();

        $spreadsheet = $this->setExcelContent($spreadsheet);

        if (Str::contains($fileName, '.xlsx', true)) {
            $writer = new Xlsx($spreadsheet);
        } else {
            $writer = new Xls($spreadsheet);
        }
        $writer->save($file);

        return $file;
    }

    public function setExcelContent(Spreadsheet $spreadsheet): Spreadsheet
    {
        $spreadsheet = $this->setFirstHeader($spreadsheet);
        $spreadsheet = $this->setSecondHeader($spreadsheet);

        return $this->setSpreadsheet($spreadsheet);
    }

    public function setFirstHeader(Spreadsheet $spreadsheet): Spreadsheet
    {
        $spreadsheet->getActiveSheet()->setCellValue('H1', 'Adresse der Apotheke');

        return $spreadsheet;
    }

    public function setSecondHeader(Spreadsheet $spreadsheet): Spreadsheet
    {
        $spreadsheet->getActiveSheet()->setCellValue('A2', 'Anrede (Auswahlfeld)');
        $spreadsheet->getActiveSheet()->setCellValue('B2', 'Akadem. Grad');
        $spreadsheet->getActiveSheet()->setCellValue('C2', 'Vorname Inhaber');
        $spreadsheet->getActiveSheet()->setCellValue('D2', 'Nachname Inhaber');
        $spreadsheet->getActiveSheet()->setCellValue('E2', 'E-Mail-Adresse Inhaber');
        $spreadsheet->getActiveSheet()->setCellValue('F2', 'OHG Name');
        $spreadsheet->getActiveSheet()->setCellValue('G2', 'Name der Apotheke');
        $spreadsheet->getActiveSheet()->setCellValue('H2', 'Adresszusatz');
        $spreadsheet->getActiveSheet()->setCellValue('I2', 'Straße + Hausnr.');
        $spreadsheet->getActiveSheet()->setCellValue('J2', 'Postleitzahl');
        $spreadsheet->getActiveSheet()->setCellValue('K2', 'Ort');
        $spreadsheet->getActiveSheet()->setCellValue('L2', 'Datum (Format: TT.MM.YYYY)');

        return $spreadsheet;
    }

    public function setSpreadsheet(Spreadsheet $spreadsheet): Spreadsheet
    {
        $spreadsheet->getActiveSheet()->setCellValue('A3', $this->faker->randomElement(SalutationEnum::getLabels()));
        $spreadsheet->getActiveSheet()->setCellValue('B3', $this->faker->title);
        $spreadsheet->getActiveSheet()->setCellValue('C3', $this->faker->firstName);
        $spreadsheet->getActiveSheet()->setCellValue('D3', $this->faker->lastName);
        $spreadsheet->getActiveSheet()->setCellValue('E3', 'qm+'.$this->emailCounter.'@gedisa.de');
        $spreadsheet->getActiveSheet()->setCellValue('F3', $this->faker->pharmacyName);
        $spreadsheet->getActiveSheet()->setCellValue('G3', $this->faker->pharmacyName);
        $spreadsheet->getActiveSheet()->setCellValue('H3', $this->faker->streetSuffix());
        $spreadsheet->getActiveSheet()->setCellValue(
            'I3',
            $this->faker->streetName.(random_int(1, 9999).' '.((bool) random_int(0, 5) ? Str::random(1) : ''))
        );
        $spreadsheet->getActiveSheet()->setCellValue('J3', random_int(10000, 99999));
        $spreadsheet->getActiveSheet()->setCellValue('K3', $this->faker->city);
        $spreadsheet->getActiveSheet()->setCellValue('L3', date('d.m.Y', strtotime(now())));

        $this->emailCounter++;

        return $spreadsheet;
    }

    public function test_get_remove_member_date_options_without_invoices_in_livewire_context_as_admin(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->call('getRemoveMemberDateOptions')
            ->assertHasNoErrors();
    }

    public function test_cancel_dates_are_in_the_future_and_end_of_quarter(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        $livewire = new AssociationMembers;
        $dateOptions = $livewire->getRemoveMemberDateOptions();

        $loop = 1;
        foreach ($dateOptions as $date => $label) {
            if ($loop === 1 && now()->isSameDay(now()->endOfQuarter())) {
                $this->assertTrue(Carbon::parse($date)->isToday());
            } else {
                $this->assertTrue(Carbon::parse($date)->isFuture());
            }
            $this->assertTrue(Carbon::parse($date)->isSameDay(Carbon::parse($date)->endOfQuarter()));
            $loop++;
        }
    }

    public function test_dont_remove_member_when_no_user_to_remove_is_set(): void
    {
        [$associationEmployee, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy();

        $oldAssociationId = $owner->pharmacyProfile->association_id;

        $this->actingAs($associationEmployee);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationEmployee->association(),
        ])
            ->set('removeMemberDate', now()->addMonthsNoOverflow())
            ->call('removeAssociationMember', $owner->id)
            ->assertHasNoErrors()
            ->assertDispatched('change-modal-state');

        $owner->refresh();

        $this->assertSame($oldAssociationId, $owner->pharmacyProfile->association_id);

        $this->assertNull($owner->currentAssociationMembershipChange);
    }

    public function test_dont_remove_member_when_no_remove_date_is_set(): void
    {
        [$associationEmployee, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy();

        $oldAssociationId = $owner->pharmacyProfile->association_id;

        $this->actingAs($associationEmployee);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationEmployee->association(),
        ])
            ->call('removeAssociationMember', $owner->id)
            ->assertHasErrors()
            ->assertNotDispatched('change-modal-state');

        $owner->refresh();

        $this->assertSame($oldAssociationId, $owner->pharmacyProfile->association_id);

        $this->assertNull($owner->currentAssociationMembershipChange);
    }

    public function test_user_can_be_edited_because_no_association_member_ship_change(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        $memberListEntries = Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])->members;

        $this->assertCount(1, $memberListEntries);
        $this->assertTrue($memberListEntries->first()->isEditable());
    }

    public function test_user_can_be_edited_because_association_member_change_is_canceled(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        AssociationMembershipChange::create([
            'user_id' => $owner->id,
            'association_id_before' => $owner->pharmacyProfile->association_id,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now()->copy()->endOfMonth(),
            'canceled_at' => now(),
        ]);

        $owner->refresh();

        $this->actingAs($associationAdmin);

        $memberListEntries = Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])->members;

        $this->assertCount(1, $memberListEntries);
        $this->assertTrue($memberListEntries->first()->isEditable());
    }

    public function test_user_cannot_be_edited_because_has_association_memberhip_is_canceled(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        AssociationMembershipChange::create([
            'user_id' => $owner->id,
            'association_id_before' => $owner->pharmacyProfile->association_id,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now()->copy()->endOfMonth(),
        ]);

        $owner->refresh();

        $this->actingAs($associationAdmin);

        $memberListEntries = Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])->members;

        $this->assertCount(1, $memberListEntries);
        $this->assertFalse($memberListEntries->first()->isEditable());
    }

    public function test_it_can_see_delete_association_membership_modal(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $owner->refresh();

        $this->actingAs($associationAdmin);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->set('userToRemove', $owner)
            ->call('openRemoveAssociationMemberModal')
            ->assertSee($owner->title.' '.$owner->last_name.', '.$owner->first_name)
            ->assertDispatched(
                'change-modal-state',
            );

    }

    private function createAssociationAdminOwnerAndPharmacy($associationRole = AssociationRoleEnum::ADMIN): array
    {
        [$associationUser, $association] = $this->createAssociationUser(
            Association::factory()->create(),
            $associationRole
        );

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = $association->id;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = $association->id;
        $owner->brochureCode->save();

        $pharmacy->association_id = $association->id;
        $pharmacy->save();

        $owner->refresh();
        $pharmacy->refresh();

        $this->assertSame(1, $pharmacy->acceptedTermsOfUse()->count());
        $this->assertSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertSame($association->id, $owner->brochureCode->association_id);
        $this->assertSame($association->id, $pharmacy->association_id);

        return [$associationUser, $owner, $pharmacy];
    }

    public function test_rendering_all_variants(): void
    {
        // noch nicht eingeloggt
        [$associationAdmin, $registered] = $this->createAssociationAdminOwnerAndPharmacy();
        // aktives Mitglied
        $active = $this->createPharmacyUser(association: $associationAdmin->association());
        $active->pharmacyProfile?->update(['association_id' => $associationAdmin->association()->getKey()]);
        $active->update(['last_login' => now()->subDays(2)]);
        // failed import mit status description
        $this->createMemberImport($this->createPharmacyUser(association: $associationAdmin->association()), MemberImportStatus::Failed, 'Fehler');
        // pending import
        $this->createMemberImport($this->createPharmacyUser(association: $associationAdmin->association()), MemberImportStatus::Pending);
        // importing
        $this->createMemberImport($this->createPharmacyUser(association: $associationAdmin->association()), MemberImportStatus::Importing);
        // löschung vorgemerkt
        $willBeRemoved = $this->createPharmacyUser(association: $associationAdmin->association());

        AssociationMembershipChange::create([
            'user_id' => $willBeRemoved->id,
            'association_id_before' => $associationAdmin->association()->getKey(),
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now()->copy()->endOfMonth(),
        ]);

        $component = Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ]);

        $this->assertCount(6, $component->members);

        $component
            ->assertViewIs('livewire.association.components.members')
            ->assertSet('association', $associationAdmin->association())
            ->assertSet('modalOpen', false)
            ->assertSet('userToRemove', null)
            ->assertSet('removeMemberDate', null)
            ->assertHasNoErrors()
            ->assertSessionHasNoErrors();
    }
}
