<?php

namespace App\Nova;

use App\Integrations\IntegrationTypeEnum;
use App\Nova\Actions\SyncPharmacyDataToIa;
use App\Nova\Filters\IaLatestSyncStatus;
use App\Nova\Metrics\ActiveIaIntegrations;
use App\Nova\Metrics\IaIntegrationDeactivationsOverTime;
use App\Nova\Metrics\IaIntegrationsOverTime;
use Illuminate\Http\Request;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Query\Search\SearchableJson;
use Laravel\Nova\Query\Search\SearchableMorphToRelation;

class IaIntegration extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Integration>
     */
    public static $model = \App\Integration::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    public static $group = 'IhreApotheken';

    /** @return array<string|SearchableJson|SearchableMorphToRelation> */
    public static function searchableColumns(): array
    {
        return [
            'id',
            new SearchableMorphToRelation('integratable', 'name', [\App\Pharmacy::class]),
            'integratable_id',
            new SearchableJson('settings->iaPharmacyId'),
        ];
    }

    public static function label()
    {
        return 'Integrationen';
    }

    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     */
    public static function indexQuery(NovaRequest $request, $query): \Illuminate\Database\Eloquent\Builder
    {
        return $query
            ->where('integration_type', IntegrationTypeEnum::IhreApotheken);
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int,\Laravel\Nova\Fields\Boolean|\Laravel\Nova\Fields\DateTime|\Laravel\Nova\Fields\HasMany|\Laravel\Nova\Fields\ID|\Laravel\Nova\Fields\Text>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make('ID', 'id')->sortable(),

            Text::make('Apo ID', function ($model) {
                return $model->integratable->id;
            })->sortable(),

            Text::make('Name', function ($model) {
                return $model->integratable->name;
            })->sortable(),

            Text::make('IA ID', function ($model) {
                return $model?->settings->iaPharmacyId;
            }),

            Boolean::make('Integration Aktiv', function ($model) {
                return $model->integratable->hasIaEnabled();
            }),

            Boolean::make('In ApoGuide gelistet', function ($model) {
                return $model->integratable->hasIaEnabled() && $model?->settings->isVisibleInPharmacySearch;
            }),

            Boolean::make('Ist ZPA Kunde', function ($model) {
                return $model?->settings->isZpaCustomer;
            }),

            Number::make('Anzahl Syncs', function ($model) {
                return $model->integratable->iaSyncs->count();
            }),

            DateTime::make('Letzter Sync am', function ($model) {
                return $model->integratable->iaSyncs()->latest()->first()?->created_at;
            })
                ->displayUsing(fn ($value) => $value?->timezone('Europe/Berlin')->format('d.m.Y H:i'))
                ->readonly()
                ->sortable(),

            Text::make('Letzter Sync erfolgreich', function ($model) {
                return $model->integratable->iaSyncs()->latest()->first()?->successful;
            })
                ->displayUsing(fn ($value) => $value === null ? null : ($value ? '✅' : '❌'))
                ->nullable()
                ->readonly()
                ->sortable(),

            DateTime::make('Erstmalig aktiviert am', 'created_at')
                ->displayUsing(fn ($value) => $value?->timezone('Europe/Berlin')->format('d.m.Y H:i'))
                ->readonly()
                ->sortable(),

            DateTime::make('Aktualisiert am', 'updated_at')
                ->displayUsing(fn ($value) => $value?->timezone('Europe/Berlin')->format('d.m.Y H:i'))
                ->readonly()
                ->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array<\Laravel\Nova\Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [
            ActiveIaIntegrations::make(),
            IaIntegrationsOverTime::make(),
            IaIntegrationDeactivationsOverTime::make(),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [
            new SyncPharmacyDataToIa,
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [
            new IaLatestSyncStatus,
        ];
    }
}
