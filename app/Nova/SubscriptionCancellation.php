<?php

namespace App\Nova;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Nova\Actions\Subscription\RevokeCancellationNovaAction;
use App\SubscriptionCancellation as SubscriptionCancellationModel;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\Badge;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphTo;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource;

class SubscriptionCancellation extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<SubscriptionCancellationModel>
     */
    public static $model = SubscriptionCancellationModel::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array<string>
     */
    public static $search = [
        'id', 'pharmacy.name',
    ];

    public static $group = 'Mitgliedschaft';

    public static function label(): string
    {
        return 'Kündigungen';
    }

    public static function singularLabel(): string
    {
        return 'Kündigung';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Apotheke', 'pharmacy', Pharmacy::class)
                ->searchable()
                ->sortable(),

            MorphTo::make('Gekündigt von', 'requestable')->types([
                User::class,
                Staff::class,
            ])->searchable()
                ->sortable(),

            DateTime::make('Beantragt am', 'requested_at')
                ->sortable(),

            Badge::make('Status', 'status')
                ->map([
                    SubscriptionCancellationStatusEnum::PENDING->value => 'warning',
                    SubscriptionCancellationStatusEnum::CONFIRMED->value => 'info',
                    SubscriptionCancellationStatusEnum::REVOKED->value => 'danger',
                    SubscriptionCancellationStatusEnum::EXECUTED->value => 'success',
                ])
                ->labels([
                    SubscriptionCancellationStatusEnum::PENDING->value => SubscriptionCancellationStatusEnum::PENDING->label(),
                    SubscriptionCancellationStatusEnum::CONFIRMED->value => SubscriptionCancellationStatusEnum::CONFIRMED->label(),
                    SubscriptionCancellationStatusEnum::REVOKED->value => SubscriptionCancellationStatusEnum::REVOKED->label(),
                    SubscriptionCancellationStatusEnum::EXECUTED->value => SubscriptionCancellationStatusEnum::EXECUTED->label(),
                ])
                ->sortable(),

            DateTime::make('Kündigungsdatum', 'effective_date')
                ->sortable(),

            DateTime::make('Bestätigt am', 'confirmed_at')
                ->hideFromIndex()
                ->nullable(),

            DateTime::make('Ausgeführt am', 'executed_at')
                ->hideFromIndex()
                ->nullable(),

            MorphTo::make('Widerrufen von', 'revokable')->types([
                User::class,
                Staff::class,
            ])->searchable()
                ->sortable(),

            DateTime::make('Widerrufen am', 'revoked_at')
                ->hideFromIndex()
                ->nullable(),

            Textarea::make('Grund', 'reason')
                ->hideFromIndex()
                ->nullable(),

            Text::make('Metadaten', function () {
                if (empty($this->model()->metadata)) {
                    return '-';
                }

                return json_encode($this->model()->metadata, JSON_PRETTY_PRINT);
            })
                ->asHtml()
                ->hideFromIndex()
                ->onlyOnDetail(),

            DateTime::make('Erstellt am', 'created_at')
                ->hideFromIndex()
                ->sortable(),

            DateTime::make('Aktualisiert am', 'updated_at')
                ->hideFromIndex()
                ->sortable(),
        ];
    }

    /**
     * @return array<Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [
            RevokeCancellationNovaAction::make()
                ->canSee(function () {
                    assert($this->resource instanceof SubscriptionCancellationModel);

                    return in_array($this->resource->status, [
                        SubscriptionCancellationStatusEnum::PENDING,
                        SubscriptionCancellationStatusEnum::CONFIRMED,
                    ]) && $this->resource->canBeRevoked();
                })
                ->canRun(function ($request, $model) {
                    assert($model instanceof SubscriptionCancellationModel);

                    return $model->canBeRevoked();
                }),
        ];
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->with(['pharmacy']);
    }
}
