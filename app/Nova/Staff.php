<?php

namespace App\Nova;

use App\Enums\StaffRoleEnum;
use App\Rules\GedisaMail;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Password;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Nova;

/**
 * @mixin \App\Staff
 */
class Staff extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Staff::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public static $group = 'Nutzerverwaltung';

    public static function label()
    {
        return 'Administratoren';
    }

    public static function singularLabel(): string
    {
        return 'Administrator';
    }

    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        if (auth('staff')->user()?->doesntHaveRole(StaffRoleEnum::ADMIN)) {
            return $query->where('id', auth('staff')->user()->id);
        }

        return $query;
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable()
                ->canSee(function ($request) {
                    return $request->user()->hasRole(StaffRoleEnum::ADMIN);
                }),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('Email')
                ->sortable()
                ->rules('required', app(GedisaMail::class))
                ->creationRules('unique:staff,email')
                ->updateRules('unique:staff,email,{{resourceId}}'),

            Select::make('Role')
                ->options(StaffRoleEnum::getForDropdown())
                ->required()
                ->displayUsingLabels()
                ->canSee(function ($request) {
                    return $request->user()->hasRole(StaffRoleEnum::ADMIN);
                }),

            Password::make('Password')
                ->onlyOnForms()
                ->creationRules('required', 'string', 'min:8')
                ->updateRules('nullable', 'string', 'min:8'),

            HasMany::make(__('Actions'), 'actionEvents', Nova::actionResource())
                ->canSee(function ($request) {
                    return auth('staff')->user()?->hasRole(StaffRoleEnum::ADMIN);
                }),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
