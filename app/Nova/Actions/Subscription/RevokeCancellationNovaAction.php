<?php

namespace App\Nova\Actions\Subscription;

use App\Actions\Subscription\RevokeSubscriptionCancellationAction;
use App\Staff;
use App\SubscriptionCancellation;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class RevokeCancellationNovaAction extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Kündigung widerrufen';

    public $confirmText = 'Soll die Kündigung widerrufen werden?';

    public function handle(ActionFields $fields, Collection $models): ActionResponse|Action
    {
        if ($models->count() > 1) {
            return Action::danger('Bitte nur eine Kündigung auswählen.');
        }

        $cancellation = $models->first();
        assert($cancellation instanceof SubscriptionCancellation);

        if (! $cancellation->canBeRevoked()) {
            return Action::danger('Diese <PERSON>ndigung kann nicht mehr widerrufen werden.');
        }

        $user = auth()->user();
        assert($user instanceof User || $user instanceof Staff);

        $notes = $fields->get('notes') ?? 'Kündigung über Nova widerrufen';
        assert(is_string($notes));

        app(RevokeSubscriptionCancellationAction::class)->execute(
            $cancellation->pharmacy,
            $user,
            $notes
        );

        return Action::message('Die Kündigung wurde erfolgreich widerrufen.');
    }

    /** @return array<Field> */
    public function fields(NovaRequest $request): array
    {
        return [
            Textarea::make('Grund für Widerruf', 'notes')
                ->rows(3)
                ->help('Bitte geben Sie den Grund für den Widerruf an.')
                ->required(),
        ];
    }
}
