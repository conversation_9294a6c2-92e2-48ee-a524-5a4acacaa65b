<?php

namespace App;

use App\Contracts\IOrderable;
use App\Data\CardLink\OrderInformationData;
use App\Domains\Subscription\Application\FeatureAccess\CardLinkFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\ChatFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\DigitalRepresentationFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\PharmaceuticalServicesFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\VaccinationFeatureAccess;
use App\Domains\Subscription\Application\Settings\TaxStripeSetting;
use App\Domains\Subscription\Application\Settings\TemplateStripeSetting;
use App\Domains\Subscription\Application\StripeProducts\AddOns\TIMStripeProduct;
use App\Domains\Subscription\Application\Traits\SubscribableNew;
use App\Enums\AssociationEnum;
use App\Enums\BusinessHourEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Enums\IbmRegistrationStatusEnum;
use App\Enums\PaymentMethod;
use App\Enums\PharmacyAddressTypeEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\PharmacyStatusEnum;
use App\Enums\PharmacyVaccinateStatusEnum;
use App\Enums\Settings\PharmacySettingTypes;
use App\Enums\SubscriptionCancellationStatusEnum;
use App\Events\PharmacySaved;
use App\Helper\ApomondoApi;
use App\Helper\CardLinkOrderHelper;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Dto\BusinessHoursDto;
use App\Integrations\IaIntegration;
use App\Integrations\IntegrationTypeEnum;
use App\Settings\ApoMailSettings;
use App\Settings\TermsOfServiceSettings;
use App\Support\KkzFinder;
use App\Traits\Deprecated\PharmacyDeprecatedTrait;
use App\Traits\HasApprovableChange;
use App\Traits\HasGeneralSettings;
use App\Traits\InteractsWithIntegrations;
use App\Traits\NotifiableTrait;
use App\Traits\NotificationClosable;
use App\Traits\Subscribable;
use Carbon\Carbon;
use Elastic\ScoutDriverPlus\Searchable;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Laravel\Cashier\Billable;
use Laravel\Nova\Actions\Actionable;
use LogicException;
use Throwable;

/**
 * Class Pharmacy
 *
 * @mixin IdeHelperPharmacy
 *
 * @phpstan-ignore-next-line
 *
 * @mixin PharmacyDeprecatedTrait
 *
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $name
 * @property string|null $institute_id
 * @property string|null $n_id
 * @property string|null $pharmacy_id
 * @property string $phone
 * @property string $fax
 * @property string $email
 * @property int $has_vat
 * @property string|null $website
 * @property string|null $instagram
 * @property string|null $facebook
 * @property string|null $commercial_register
 * @property bool $courier_service
 * @property int|null $courier_service_radius
 * @property int $has_near_parking_space
 * @property int|null $goods_management_system_id
 * @property string|null $ibm_id
 * @property int|null $association_id
 * @property bool $does_influenza_vaccination
 * @property bool $does_covid_vaccination
 * @property bool $uses_calendar
 * @property string|null $calendar_uuid
 * @property string|null $calendar_email
 * @property int $does_pharmaceutical_services
 * @property string $is_ibm_registered
 * @property int|null $billing_address_id
 * @property bool $uses_chat
 * @property bool $uses_patient_chat
 * @property bool $uses_telepharmacy
 * @property bool $uses_sdr
 * @property bool $uses_apomail
 * @property int|null $subscription_service_id
 * @property string|null $stripe_id
 * @property string|null $pm_type
 * @property PaymentMethod|null $selected_pm_type
 * @property string|null $pm_last_four
 * @property string|null $trial_ends_at
 * @property bool $send_invoices
 * @property-read \App\AccountingCenter|null $accountingCenter
 * @property-read Collection<int, \Laravel\Nova\Actions\ActionEvent> $actions
 * @property-read int|null $actions_count
 * @property-read Collection<int, \App\SubscriptionOrder> $activeSubscriptionOrders
 * @property-read int|null $active_subscription_orders_count
 * @property-read \App\AdvertisingMaterial|null $advertisingMaterial
 * @property-read Collection<int, \App\ApprovableChange> $approvableChanges
 * @property-read int|null $approvable_changes_count
 * @property-read \App\Association|null $association
 * @property-read \App\BillingAddress|null $billingAddress
 * @property-read Collection<int, \App\BusinessHour> $businessHours
 * @property-read int|null $business_hours_count
 * @property-read Collection<int, \App\CalendarTopic> $calendarTopics
 * @property-read int|null $calendar_topics_count
 * @property-read \App\CardLinkOrder|null $cardLinkOrder
 * @property-read \App\CardLinkPartnerPharmacy|null $cardLinkPartnerPharmacy
 * @property-read Collection<int, \App\ClosedNotification> $closeableNotifications
 * @property-read int|null $closeable_notifications_count
 * @property-read Collection<int, \App\CovidVaccinationInvoice> $covidVaccinationInvoices
 * @property-read int|null $covid_vaccination_invoices_count
 * @property-read Collection<int, \App\CovidVaccination> $covidVaccinations
 * @property-read int|null $covid_vaccinations_count
 * @property-read Collection<int, \App\DocSpaceGroup> $docSpaceGroups
 * @property-read int|null $doc_space_groups_count
 * @property-read Collection<int, \App\DocSpace> $docSpaces
 * @property-read int|null $doc_spaces_count
 * @property-read \App\PharmacyRoleUser|\App\LanguagePharmacy|\App\FocusAreaPharmacy|null $pivot
 * @property-read Collection<int, \App\FocusArea> $focusAreas
 * @property-read int|null $focus_areas_count
 * @property-read \App\GedisaId|null $gedisaId
 * @property-read Collection<int, \App\Setting> $generalSettings
 * @property-read int|null $general_settings_count
 * @property-read string $address
 * @property-read string $city
 * @property-read string $house_number
 * @property-read mixed|null $kkz
 * @property-read int|float|null $latitude
 * @property-read int|float|null $longitude
 * @property-read string|null $optional_address_line
 * @property-read mixed $pending
 * @property-read string $postcode
 * @property-read string $street
 * @property-read string $suffix
 * @property-read string $upper_short_name
 * @property-read \App\GoodsManagementSystem|null $goodsManagementSystem
 * @property-read Collection<int, \App\GuidedDocspaceCreationProcess> $guidedDocspaceCreationProcesses
 * @property-read int|null $guided_docspace_creation_processes_count
 * @property-read \App\PharmacyAddress|null $homeAddress
 * @property-read \App\IaOrder|null $iaOrder
 * @property-read Collection<int, \App\IaSync> $iaSyncs
 * @property-read int|null $ia_syncs_count
 * @property-read \App\IbmRegistration|null $ibmRegistration
 * @property-read Collection<int, \App\PharmacyImage> $images
 * @property-read int|null $images_count
 * @property-read Collection<int, \App\InfluenzaVaccinationInvoice> $influenzaVaccinationInvoices
 * @property-read int|null $influenza_vaccination_invoices_count
 * @property-read Collection<int, \App\InfluenzaVaccination> $influenzaVaccinations
 * @property-read int|null $influenza_vaccinations_count
 * @property-read Collection<int, \App\InhalationTechnique> $inhalationTechniques
 * @property-read int|null $inhalation_techniques_count
 * @property-read Collection<int, \App\Integration> $integrations
 * @property-read int|null $integrations_count
 * @property-read Collection<int, \App\KimAddress> $kimAddresses
 * @property-read int|null $kim_addresses_count
 * @property-read Collection<int, \App\Language> $languages
 * @property-read int|null $languages_count
 * @property-read \App\IaSync|null $latestIaSync
 * @property-read \App\PharmacyImage|null $logo
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \App\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection<int, \App\Subscription> $oldSubscriptions
 * @property-read int|null $old_subscriptions_count
 * @property-read Collection<int, \App\User> $owners
 * @property-read int|null $owners_count
 * @property-read Collection<int, \App\PharmaceuticalServiceInvoice> $pharmaceuticalServiceInvoices
 * @property-read int|null $pharmaceutical_service_invoices_count
 * @property-read Collection<int, \App\PharmaceuticalServiceType> $pharmaceuticalServiceTypes
 * @property-read int|null $pharmaceutical_service_types_count
 * @property-read Collection<int, \App\PharmaceuticalService> $pharmaceuticalServices
 * @property-read int|null $pharmaceutical_services_count
 * @property-read Collection<int, \App\PharmacyAddress> $pharmacyAddresses
 * @property-read int|null $pharmacy_addresses_count
 * @property-read Collection<int, \App\PharmacyImage> $pharmacyImages
 * @property-read int|null $pharmacy_images_count
 * @property-read \App\PharmacyStatistic|null $pharmacyStatistic
 * @property-read \App\PharmacyType|null $pharmacyType
 * @property-read Collection<int, \App\PublicTransportStation> $publicTransportStations
 * @property-read int|null $public_transport_stations_count
 * @property-read \App\PharmacySetting|null $settings
 * @property-read \App\StripeMigrationOrder|null $stripeMigrationOrder
 * @property-read Collection<int, \Laravel\Cashier\SubscriptionItem> $subscriptionItems
 * @property-read int|null $subscription_items_count
 * @property-read Collection<int, \App\SubscriptionOrder> $subscriptionOrders
 * @property-read int|null $subscription_orders_count
 * @property-read Collection<int, \Laravel\Cashier\Subscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \App\TelematicsId|null $telematicsId
 * @property-read Collection<int, \App\User> $users
 * @property-read int|null $users_count
 * @property-read \App\PharmacyAddress|null $vaccinationImportAddress
 * @property-read Collection<int, \App\VaccinationImportInvoice> $vaccinationImportInvocies
 * @property-read int|null $vaccination_import_invocies_count
 * @property-read Collection<int, \App\VaccinationImport> $vaccinationImports
 * @property-read int|null $vaccination_imports_count
 * @property-read Collection<int, \App\Vaccination> $vaccinations
 * @property-read int|null $vaccinations_count
 *
 * @method static Builder<static>|Pharmacy acceptedTermsOfUse()
 * @method static Builder<static>|Pharmacy acceptedTermsOfUseAfter(\Carbon\Carbon $datetime)
 * @method static Builder<static>|Pharmacy acceptedTermsOfUseAfterApomail()
 * @method static Builder<static>|Pharmacy acceptedTermsOfUseBeforeApomail()
 * @method static Builder<static>|Pharmacy areSearchable()
 * @method static \Database\Factories\PharmacyFactory factory($count = null, $state = [])
 * @method static Builder<static>|Pharmacy hasAssociation(\App\Association $association)
 * @method static Builder<static>|Pharmacy hasExpiredGenericTrial()
 * @method static Builder<static>|Pharmacy newModelQuery()
 * @method static Builder<static>|Pharmacy newQuery()
 * @method static Builder<static>|Pharmacy onGenericTrial()
 * @method static Builder<static>|Pharmacy query()
 * @method static Builder<static>|Pharmacy vaccinationActive()
 * @method static Builder<static>|Pharmacy whereAcceptsEPrescription($value)
 * @method static Builder<static>|Pharmacy whereAccountingCenterId($value)
 * @method static Builder<static>|Pharmacy whereActive($value)
 * @method static Builder<static>|Pharmacy whereAssociationId($value)
 * @method static Builder<static>|Pharmacy whereBillingAddressId($value)
 * @method static Builder<static>|Pharmacy whereCalendarEmail($value)
 * @method static Builder<static>|Pharmacy whereCalendarUuid($value)
 * @method static Builder<static>|Pharmacy whereCommercialRegister($value)
 * @method static Builder<static>|Pharmacy whereCoronaRapidTest($value)
 * @method static Builder<static>|Pharmacy whereCoronaRapidTestBookingUrl($value)
 * @method static Builder<static>|Pharmacy whereCourierService($value)
 * @method static Builder<static>|Pharmacy whereCourierServiceRadius($value)
 * @method static Builder<static>|Pharmacy whereCreatedAt($value)
 * @method static Builder<static>|Pharmacy whereCustomAccountingCenter($value)
 * @method static Builder<static>|Pharmacy whereCustomGoodsManagementSystem($value)
 * @method static Builder<static>|Pharmacy whereDoesCovidVaccination($value)
 * @method static Builder<static>|Pharmacy whereDoesInfluenzaVaccination($value)
 * @method static Builder<static>|Pharmacy whereDoesPharmaceuticalServices($value)
 * @method static Builder<static>|Pharmacy whereEmail($value)
 * @method static Builder<static>|Pharmacy whereExportAddedValueToGematik($value)
 * @method static Builder<static>|Pharmacy whereFacebook($value)
 * @method static Builder<static>|Pharmacy whereFax($value)
 * @method static Builder<static>|Pharmacy whereGoodsManagementSystemId($value)
 * @method static Builder<static>|Pharmacy whereHasNearParkingSpace($value)
 * @method static Builder<static>|Pharmacy whereHasVat($value)
 * @method static Builder<static>|Pharmacy whereIbmId($value)
 * @method static Builder<static>|Pharmacy whereId($value)
 * @method static Builder<static>|Pharmacy whereInstagram($value)
 * @method static Builder<static>|Pharmacy whereInstituteId($value)
 * @method static Builder<static>|Pharmacy whereIsIbmRegistered($value)
 * @method static Builder<static>|Pharmacy whereIsMain($value)
 * @method static Builder<static>|Pharmacy whereNId($value)
 * @method static Builder<static>|Pharmacy whereName($value)
 * @method static Builder<static>|Pharmacy wherePharmacyId($value)
 * @method static Builder<static>|Pharmacy wherePharmacyType($value)
 * @method static Builder<static>|Pharmacy wherePhone($value)
 * @method static Builder<static>|Pharmacy wherePmLastFour($value)
 * @method static Builder<static>|Pharmacy wherePmType($value)
 * @method static Builder<static>|Pharmacy whereSelectedPmType($value)
 * @method static Builder<static>|Pharmacy whereSendInvoices($value)
 * @method static Builder<static>|Pharmacy whereShippingPharmacyEnabled($value)
 * @method static Builder<static>|Pharmacy whereShippingPharmacyName($value)
 * @method static Builder<static>|Pharmacy whereShippingPharmacyWebsite($value)
 * @method static Builder<static>|Pharmacy whereShowInApoguide($value)
 * @method static Builder<static>|Pharmacy whereStripeId($value)
 * @method static Builder<static>|Pharmacy whereSubscriptionServiceId($value)
 * @method static Builder<static>|Pharmacy whereTrialEndsAt($value)
 * @method static Builder<static>|Pharmacy whereUpdatedAt($value)
 * @method static Builder<static>|Pharmacy whereUsesApomail($value)
 * @method static Builder<static>|Pharmacy whereUsesCalendar($value)
 * @method static Builder<static>|Pharmacy whereUsesChat($value)
 * @method static Builder<static>|Pharmacy whereUsesPatientChat($value)
 * @method static Builder<static>|Pharmacy whereUsesSdr($value)
 * @method static Builder<static>|Pharmacy whereUsesTelepharmacy($value)
 * @method static Builder<static>|Pharmacy whereUuid($value)
 * @method static Builder<static>|Pharmacy whereVaccinationImport($value)
 * @method static Builder<static>|Pharmacy whereVerificationStatus($value)
 * @method static Builder<static>|Pharmacy whereWebsite($value)
 * @method static Builder<static>|Pharmacy withFeatureAccess(string $featureAccess)
 *
 * @property int|null $accounting_center_id
 * @property bool $active
 * @property int $verification_status
 * @property bool $is_main
 * @property int|null $pharmacy_type
 * @property string|null $custom_goods_management_system
 * @property string|null $custom_accounting_center
 * @property string|null $uuid
 * @property bool $corona_rapid_test
 * @property bool $show_in_apoguide
 * @property bool $export_added_value_to_gematik
 * @property string|null $corona_rapid_test_booking_url
 * @property bool $shipping_pharmacy_enabled
 * @property string|null $shipping_pharmacy_name
 * @property string|null $shipping_pharmacy_website
 * @property bool $vaccination_import
 * @property bool $accepts_e_prescription
 * @property int $uses_retax
 *
 * @method static Builder<static>|Pharmacy whereUsesRetax($value)
 *
 * @property-read \App\SubscriptionCancellation|null $subscriptionCancellation
 *
 * @mixin \Eloquent
 */
class Pharmacy extends Model implements IOrderable
{
    use Actionable;
    use Billable {
        createAsStripeCustomer as protected createAsStripeCustomerTrait;
    }
    use HasApprovableChange;
    use HasFactory;
    use HasGeneralSettings;
    use InteractsWithIntegrations;
    use NotifiableTrait;
    use NotificationClosable;
    use Searchable;
    use Subscribable;
    use SubscribableNew;

    protected $needsApproval = [];

    protected $casts = [
        'courier_service' => 'boolean',
        'show_in_apoguide' => 'boolean',
        'export_added_value_to_gematik' => 'boolean',
        'shipping_pharmacy_enabled' => 'boolean',
        'does_covid_vaccination' => 'boolean',
        'does_influenza_vaccination' => 'boolean',
        'uses_calendar' => 'boolean',
        'uses_chat' => 'boolean',
        'uses_patient_chat' => 'boolean',
        'uses_sdr' => 'boolean',
        'uses_telepharmacy' => 'boolean',
        'uses_apomail' => 'boolean',
        'send_invoices' => 'boolean',
        'selected_pm_type' => PaymentMethod::class,
    ];

    protected $guarded = [
        'is_active',
        'n_id', // deprecated
    ];

    /** @var array<class-string> */
    protected $dispatchesEvents = [
        'saved' => PharmacySaved::class,
    ];

    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    public function getFilesDirectory($leading = true): string
    {
        return ($leading ? '/' : '').'pharmacies/'.$this->id.'/';
    }

    public function searchableAs(): string
    {
        return config('scout.prefix').'pharmacies_index';
    }

    public function shouldBeSearchable(): bool
    {
        return ! is_null($this->owner());
    }

    /** @param $builder Builder<Pharmacy> */
    public function scopeAreSearchable(Builder $builder): Builder
    {
        return $builder->whereHas('owners');
    }

    public function toSearchableArray(): array
    {
        $owner = $this->owner();

        $general = [
            'uuid' => $this->uuid,
            'internal_id' => $this->id,
            'name' => $this->name,
            'is_disabled' => (bool) $this->is_disabled,
            'association_id' => $owner->pharmacyProfile->association->id ?? 0,
            'association_name' => $owner?->pharmacyProfile?->association?->name,
            'association_abbreviation' => $owner?->pharmacyProfile?->association?->abbreviation(),

            'gedisa_id' => $this->gedisaId?->id,
            'ik_number' => $this->institute_id,

            'phone' => $this->phone,
            'fax' => $this->fax,
            'institute_id' => $this->institute_id,
            'telematik_id' => $this->telematicsId?->fullId(),
            'city' => optional($this->homeAddress)->city,
            'zip' => optional($this->homeAddress)->postcode,
            'state' => optional($this->homeAddress)->state?->short_name,
            'street' => $this->homeAddress?->street,
            'house_number' => $this->homeAddress?->house_number,
            'optional_address_line' => $this->homeAddress?->optional_address_line,
            'address' => (optional($this->homeAddress)->optional_address_line ? optional(
                $this->homeAddress
            )->optional_address_line.', ' : null).optional($this->homeAddress)->street.' '.optional(
                $this->homeAddress
            )->house_number.', '.optional($this->homeAddress)->postcode.' '.optional($this->homeAddress)->city,
            'location' => (optional($this->homeAddress)->latitude ? [
                'lat' => optional($this->homeAddress)->latitude,
                'lon' => optional($this->homeAddress)->longitude,
            ] : []),
            'vaccination_import' => (bool) $this->vaccination_import,
            'does_pharmaceutical_services' => (bool) $this->does_pharmaceutical_services,
            'does_influenza_vaccination' => (bool) $this->does_influenza_vaccination,
            'does_covid_vaccination' => (bool) $this->does_covid_vaccination,

            'owner' => $this->owner()
                ? [
                    'title' => $this->owner()->title,
                    'salutation' => $this->owner()->salutation,
                    'first_name' => $this->owner()->first_name,
                    'last_name' => $this->owner()->last_name,
                ]
                : [],

            'contact' => [
                'email' => $this->email,
                'phone' => $this->phone,
                'website' => $this->website,
                'fax' => $this->fax,
            ],

            'languages' => $this->languages->map(
                fn (Language $language) => [
                    'id' => $language->id,
                    'code' => $language->code,
                    'full_name' => __('languages.'.$language->code),
                ]
            )->toArray(),

            'matrix_users' => $this->uses_patient_chat ? $this->users()->where('matrix_bootstrap_done', true)->get()->map(fn (User $user) => $user->mxid)->filter()->toArray() : [],
            'images' => [
                'logo' => $this->logo
                    ? url(route('public.pharmacy-image.show', $this->logo))
                    : '',
                'header' => $this->images->count() > 0
                    ? url(route('public.pharmacy-image.show', $this->images->first()))
                    : '',
            ],

            'show_in_apoguide' => (bool) $this->show_in_apoguide,
            'export_added_to_gematik' => (bool) $this->export_added_value_to_gematik,
            'accepts_e_prescription' => (bool) $this->accepts_e_prescription,

            'focus_areas' => $this->focusAreas->map(
                fn (FocusArea $focusArea) => [
                    'id' => $focusArea->id,
                    'name' => $focusArea->name,
                    'full_name' => __('entities.focusAreas.'.$focusArea->name),
                ]
            )->toArray(),

            'pharmaceutical_services' => $this->pharmaceuticalServiceTypes->map(fn ($topic) => [
                'id' => $topic->id,
                'name' => $topic->name,
            ])->toArray(),
            'calendar_url' => $this->calendar_uuid != null && ($this->uses_calendar || $this->uses_telepharmacy) ? ApomondoApi::getUrl(
                $this
            ) : null,
            'pharmacy_type' => optional($this->pharmacy_type)->name,
            'corona_rapid_test' => [
                'enabled' => (bool) $this->corona_rapid_test,
                'website' => $this->corona_rapid_test_booking_url,
            ],
            'courier_service' => [
                'enabled' => (bool) $this->courier_service,
                'radius' => $this->courier_service_radius,
            ],
            'business_hours' => collect($this->businessHours)->map(fn (BusinessHour $businessHour) => [
                'id' => $businessHour->id,
                'day_of_week' => BusinessHourEnum::getApiLabel($businessHour->day_of_week),
                'opens' => \Carbon\Carbon::now()->setTime(substr($businessHour->opens, 0, 2), substr($businessHour->opens, 3, 2), 00)->secondsSinceMidnight(),
                'closes' => \Carbon\Carbon::now()->setTime(substr($businessHour->closes, 0, 2), substr($businessHour->closes, 3, 2), 00)->secondsSinceMidnight(),
            ])->toArray(),
            'has_near_parking_space' => (bool) $this->has_near_parking_space,
            'shipping_pharmacy' => [
                'enabled' => (bool) $this->shipping_pharmacy_enabled,
                'name' => $this->shipping_pharmacy_name,
                'website' => $this->shipping_pharmacy_website,
            ],

            'show_in_services' => DigitalRepresentationFeatureAccess::check($this)->canUse(),

            'uses_retax' => (bool) $owner?->can('use-retax', $this),
            'retax_docspace_id' => $this->docSpaces()->where('used_by_retax', true)->first()?->sdr_doc_space_id,
            'main_pharmacy' => $owner?->mainPharmacy()?->uuid,

            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

        ];

        $vaccination = VaccinationFeatureAccess::check($this)->canUse() ? [
            'does_influenza_vaccination' => (bool) $this->does_influenza_vaccination,
            'does_covid_vaccination' => (bool) $this->does_covid_vaccination,
        ] : [
            'does_influenza_vaccination' => false,
            'does_covid_vaccination' => false,
        ];

        $matrixUsers = [
            'matrix_users' => ChatFeatureAccess::check($this)->canUse()
                ? $this->users()->where('matrix_bootstrap_done', true)->get()->map(fn (User $user) => $user->mxid)->filter()->toArray()
                : [],
        ];

        $vaccinationImport = [
            'vaccination_import' => false,
        ];

        $pharmaceuticalServices = [
            'does_pharmaceutical_services' => PharmaceuticalServicesFeatureAccess::check($this)->canUse()
                && $this->does_pharmaceutical_services,
        ];

        $telepharmacy = [
            'uses_telepharmacy' => $owner?->can('activateTelepharmacy', $this)
                && (bool) $this->uses_telepharmacy,
        ];

        $calendar = $owner?->can('activateCalendar', $this) ? [
            'uses_calendar' => (bool) $this->uses_calendar,
            'calendar_url' => $this->calendar_uuid && ($this->uses_calendar || $this->uses_telepharmacy) ? ApomondoApi::getUrl($this) : null,
            'calendar_uuid' => $this->calendar_uuid,
            'calendar_topics' => $this->calendarTopics->map(fn ($topic) => [
                'id' => $topic->id,
                'name' => $topic->name,
            ]),
        ] : [
            'uses_calendar' => false,
            'calendar_url' => null,
            'calendar_uuid' => null,
            'calendar_topics' => [],
        ];

        $chat = Gate::forUser($owner)->allows('activateChat', $this) && Gate::forUser($owner)->allows('chat', [$owner, $this]) && $this->canUseProduct(TIMStripeProduct::class)
            ? [
                'uses_pharmacy_chat' => $this->uses_chat,
                'uses_patient_chat' => $this->users()->where('matrix_bootstrap_done', true)->exists() && $this->uses_patient_chat,
            ]
            : [
                'uses_pharmacy_chat' => false,
                'uses_patient_chat' => false,
            ];

        $externalId = null;
        $isVisibleInPharmacySearch = false;

        if ($this->hasIaEnabled()) {
            /** @var IaIntegration|null $iaIntSettings */
            $iaIntSettings = $this->getIntegration(IntegrationTypeEnum::IhreApotheken)?->settings;

            if ($iaIntSettings) {
                $externalId = $iaIntSettings->iaPharmacyId;
                $isVisibleInPharmacySearch = $iaIntSettings->isVisibleInPharmacySearch;
            }
        }

        $enabledForApoGuide = false;
        $enabledCardLink = false;

        if ($this->cardLinkOrder !== null) {
            $orderInformation = OrderInformationData::fromCardLinkOrder($this->cardLinkOrder);
            $enabledCardLink = CardLinkFeatureAccess::check($this)->canUse();

            $enabledForApoGuide = $enabledCardLink
                && $orderInformation?->activateApoGuideVendor
                && (
                    $orderInformation->package === CardLinkPackageEnum::Unlimited
                    || CardLinkOrderHelper::settings($this)?->remainingTransactions() > 0
                )
                && $this->users()->where('matrix_bootstrap_done', true)->exists();
        }

        $externalServices = [
            'external_services' => [
                'ihre_apotheken' => [
                    'enabled' => $this->hasIaEnabled() && $isVisibleInPharmacySearch,
                    'external_id' => $isVisibleInPharmacySearch ? $externalId : null,
                ],

                'card_link' => [
                    'apoguide_cardlink_enabled' => $enabledForApoGuide,
                    'enabled' => $enabledCardLink,
                ],
            ],
        ];

        return array_merge(
            $general,
            $matrixUsers,
            $pharmaceuticalServices,
            $vaccinationImport,
            $vaccination,
            $telepharmacy,
            $calendar,
            $chat,
            $externalServices
        );
    }

    /**
     * @param  null|string  $disk
     *
     * @throws Exception
     * @throws Exception
     */
    public function delete($disk = null): ?bool
    {
        $disk = $disk ?? config('filesystems.default');
        $path = $this->getFilesDirectory();

        \Sentry\addBreadcrumb(
            new \Sentry\Breadcrumb(
                \Sentry\Breadcrumb::LEVEL_INFO,
                \Sentry\Breadcrumb::TYPE_DEFAULT,
                'Files', // category
                'Deleting directory while deleting pharmacy in Pharmacy::delete', // message (optional)
                [
                    'disk' => $disk,
                    'image' => $path,
                    'pharmacy' => $this->id,
                ] // data (optional)
            )
        );

        Storage::disk($disk)->deleteDirectory($path);

        return parent::delete();
    }

    public function stripeMigrationOrder(): HasOne
    {
        return $this->hasOne(StripeMigrationOrder::class);
    }

    public function languages(): BelongsToMany
    {
        return $this->belongsToMany(Language::class)->using(LanguagePharmacy::class)->withTimestamps();
    }

    public function focusAreas(): BelongsToMany
    {
        return $this->belongsToMany(FocusArea::class)->using(FocusAreaPharmacy::class)->withTimestamps();
    }

    public function businessHours(): HasMany
    {
        return $this->hasMany(BusinessHour::class);
    }

    public function pharmacyStatistic(): HasOne
    {
        return $this->hasOne(PharmacyStatistic::class);
    }

    public function calendarTopics(): BelongsToMany
    {
        return $this->belongsToMany(CalendarTopic::class);
    }

    public function gedisaId(): HasOne
    {
        return $this->hasOne(GedisaId::class);
    }

    public function pharmaceuticalServiceTypes()
    {
        return $this->belongsToMany(PharmaceuticalServiceType::class);
    }

    public function users(): BelongsToMany
    {
        return $this
            ->belongsToMany(User::class, 'pharmacy_role_user')
            ->using(PharmacyRoleUser::class)
            ->withPivot(['role_name', 'permissions'])
            ->withTimestamps();
    }

    public function owners(): BelongsToMany
    {
        return $this
            ->users()
            ->wherePivot('role_name', PharmacyRoleEnum::OWNER)
            ->withTimestamps();
    }

    public function pharmacyImages(): HasMany
    {
        return $this->hasMany(PharmacyImage::class);
    }

    public function publicTransportStations(): HasMany
    {
        return $this->hasMany(PublicTransportStation::class);
    }

    public function pharmacyType(): BelongsTo
    {
        return $this->belongsTo(PharmacyType::class, 'pharmacy_type');
    }

    public function goodsManagementSystem(): BelongsTo
    {
        return $this->belongsTo(GoodsManagementSystem::class);
    }

    public function accountingCenter(): BelongsTo
    {
        return $this->belongsTo(AccountingCenter::class);
    }

    public function logo(): HasOne
    {
        return $this->hasOne(PharmacyImage::class)->ofMany([], function ($query) {
            $query->where('is_logo', '=', true);
        });
    }

    public function images(): HasMany
    {
        return $this->pharmacyImages()->where('is_logo', '=', false);
    }

    public function pharmacyAddresses(): HasMany
    {
        return $this->hasMany(PharmacyAddress::class);
    }

    /**
     * @return HasOne<PharmacyAddress>
     */
    public function homeAddress(): HasOne
    {
        return $this->hasOne(PharmacyAddress::class)->ofMany([], function ($query) {
            $query->where('type', '=', PharmacyAddressTypeEnum::HOME);
        });
    }

    public function vaccinationImportAddress(): HasOne
    {
        return $this->hasOne(PharmacyAddress::class)->ofMany([], function ($query) {
            $query->where('type', '=', PharmacyAddressTypeEnum::VACCINATION_IMPORT);
        });
    }

    public function hasVaccinationImportAddress(): bool
    {
        return ! is_null($this->vaccinationImportAddress);
    }

    public function owner(): ?User
    {
        return $this->users->where('pivot.role_name', PharmacyRoleEnum::OWNER)->first();
    }

    public function ownerOrFail(): User
    {
        return $this->owner() ?? throw new LogicException('Pharmacy has no owner. '.$this->id);
    }

    public function ownersAndSubowners(): Collection
    {
        return $this->users
            ->whereIn('pivot.role_name', [
                PharmacyRoleEnum::OWNER,
                PharmacyRoleEnum::SUB_OWNER,
            ]);
    }

    public function siblingPharmacies(): Collection
    {
        return $this
            ->users
            ->where('pivot.role_name', PharmacyRoleEnum::OWNER)
            ->first()
            ->pharmacies
            ->where('id', '!=', $this->id);
    }

    public function childPharmacies(): \Illuminate\Support\Collection|Collection
    {
        if (! $this->is_main) {
            return collect([]);
        }

        return $this
            ->users
            ->where('pivot.role_name', PharmacyRoleEnum::OWNER)
            ->first()
            ->pharmacies
            ->where('is_main', false);
    }

    public function mainPharmacy(): ?Pharmacy
    {
        if ($this->is_main) {
            return null;
        }

        return $this
            ->users
            ->where('pivot.role_name', PharmacyRoleEnum::OWNER)
            ->first()
            ->pharmacies
            ->where('is_main', true)
            ->first();
    }

    public function settings(): HasOne
    {
        return $this->hasOne(PharmacySetting::class);
    }

    public function pharmaceuticalServices(): HasMany
    {
        return $this->hasMany(PharmaceuticalService::class, 'pharmacy_id', 'id');
    }

    public function inhalationTechniques(): HasMany
    {
        return $this->hasMany(InhalationTechnique::class);
    }

    public function vaccinations(): HasMany
    {
        return $this->hasMany(Vaccination::class);
    }

    public function telematicsId(): MorphOne
    {
        return $this->morphOne(TelematicsId::class, 'telematics_idable');
    }

    public function vaccinationImports(): HasMany
    {
        return $this->hasMany(VaccinationImport::class);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }

    public function vaccinationImportInvocies(): HasMany
    {
        return $this->hasMany(VaccinationImportInvoice::class);
    }

    public function covidVaccinationInvoices(): HasMany
    {
        return $this->hasMany(CovidVaccinationInvoice::class);
    }

    public function influenzaVaccinationInvoices(): HasMany
    {
        return $this->hasMany(InfluenzaVaccinationInvoice::class);
    }

    public function pharmaceuticalServiceInvoices(): HasMany
    {
        return $this->hasMany(PharmaceuticalServiceInvoice::class);
    }

    public function kimAddresses(): HasMany
    {
        return $this->hasMany(KimAddress::class, 'pharmacy_id');
    }

    public function assignUser(User $user, string $role, array $permissions = []): void
    {
        $this->users()->attach($user, ['role_name' => $role, 'permissions' => $permissions]);
    }

    public function unassignUser(User $user): void
    {
        $this->users()->detach($user);
    }

    public function reassignUser(User $user, string $role, array $permissions = []): void
    {
        $this->unassignUser($user);
        $this->assignUser($user, $role, $permissions);
    }

    public function setCourierServiceAttribute($value): void
    {
        $this->attributes['courier_service'] = (bool) $value;
    }

    public function setHasNearParkingSpaceAttribute($value): void
    {
        $this->attributes['has_near_parking_space'] = (bool) $value;
    }

    public function setCoronaRapidTestAttribute($value): void
    {
        $this->attributes['corona_rapid_test'] = (bool) $value;
    }

    public function getAddressAttribute(): string
    {
        return ($this->optional_address_line ? $this->optional_address_line.', ' : null)
            .$this->street.' '.$this->house_number.', '
            .$this->postcode.' '.$this->city;
    }

    public function getCityAttribute(): string
    {
        return $this->homeAddress ? $this->homeAddress->city : '';
    }

    public function getPostcodeAttribute(): string
    {
        return $this->homeAddress ? $this->homeAddress->postcode : '';
    }

    public function getStreetAttribute(): string
    {
        return $this->homeAddress ? $this->homeAddress->street : '';
    }

    public function getHouseNumberAttribute(): string
    {
        return $this->homeAddress ? $this->homeAddress->house_number : '';
    }

    public function getOptionalAddressLineAttribute(): ?string
    {
        return $this->homeAddress ? $this->homeAddress->optional_address_line : '';
    }

    public function getLatitudeAttribute(): float|int|null
    {
        return $this->homeAddress ? $this->homeAddress->latitude : 0;
    }

    public function getLongitudeAttribute(): float|int|null
    {
        return $this->homeAddress ? $this->homeAddress->longitude : 0;
    }

    /**
     * @deprecated Use the integration instead: $this->getIntegration(IntegrationTypeEnum::NGDA)
     */
    public function getNIdAttribute(): ?string
    {
        return $this->attributes['n_id'];
    }

    /**
     * @deprecated Use the integration instead: $this->getIntegration(IntegrationTypeEnum::NGDA)
     */
    public function setNIdAttribute(?string $value): void
    {
        $this->attributes['n_id'] = $value;
    }

    /**
     * @throws Throwable
     */
    public function storeImage($filePath, $isLogo = false): void
    {
        $file = Storage::disk('temp')->get($filePath);

        throw_if(! $file, new Exception('File not found in temp directory.'));

        $path = $this->getFilesDirectory().$filePath;

        \Sentry\addBreadcrumb(
            new \Sentry\Breadcrumb(
                \Sentry\Breadcrumb::LEVEL_INFO,
                \Sentry\Breadcrumb::TYPE_DEFAULT,
                'Files', // category
                'Storing single file while creating or updating pharmacy in Pharmacy::storeImage', // message (optional)
                [
                    /** @phpstan-ignore-next-line for safety reasons if someone adds a disk and forget to change this line */
                    'disk' => $disk ?? config('filesystems.default'),
                    'image' => $path,
                    'file' => $file,
                    'pharmacy' => $this->id,
                ] // data (optional)
            )
        );

        Storage::put($path, $file);

        $this->pharmacyImages()->create([
            'path' => $this->getFilesDirectory(false).$filePath,
            'is_logo' => $isLogo,
        ]);
    }

    public function pending(): bool
    {
        return $this->verification_status === PharmacyStatusEnum::PENDING;
    }

    public function rejected(): bool
    {
        return $this->verification_status === PharmacyStatusEnum::REJECTED;
    }

    public function verified(): bool
    {
        return $this->verification_status === PharmacyStatusEnum::VERIFIED;
    }

    public function canImportVaccinations(): bool
    {
        return $this->telematicsId && $this->vaccination_import;
    }

    private function getAssociation(Pharmacy $pharmacy): Association|false
    {
        if (! ($pharmacy->owner() && $pharmacy->owner()->pharmacyProfile && $pharmacy->owner()->pharmacyProfile->association)) {
            return false;
        }

        /** @var Association $association */
        return $pharmacy->owner()->pharmacyProfile->association;
    }

    public function addToAssociation(int $associationId): bool
    {
        if (
            ! ($association = Association::find($associationId))
            || $this->owner()?->pharmacyProfile()?->first()?->association_id !== $associationId
        ) {
            return false;
        }

        $this->association_id = $association->id;
        $this->save();

        return true;
    }

    public function getKkzAttribute(): mixed
    {
        return app(KkzFinder::class)->findForPlz($this->postcode);
    }

    public function covidVaccinations(): HasManyThrough
    {
        return $this->hasManyThrough('App\CovidVaccination', 'App\Vaccination');
    }

    public function influenzaVaccinations(): HasManyThrough
    {
        return $this->hasManyThrough('App\InfluenzaVaccination', 'App\Vaccination');
    }

    /** SCOPES **/
    public function scopeHasAssociation(Builder $query, Association $association): Builder
    {
        return $query
            ->whereHas('users.pharmacyProfile.association', function ($query) use ($association) {
                return $query->where('id', '=', $association->id);
            });
    }

    public function scopeVaccinationActive(Builder $query): Builder
    {
        return $query
            ->whereHas('settings', function ($query) {
                return $query->where('vaccinate_status', '=', PharmacyVaccinateStatusEnum::ACTIVE);
            });
    }

    public function scopeAcceptedTermsOfUse(Builder $builder): Builder
    {
        return $builder
            ->whereHas('generalSettings', fn (Builder $builder2) => $builder2->accepted(PharmacySettingTypes::TERMS_OF_USE))
            ->whereHas('generalSettings', fn (Builder $builder2) => $builder2->accepted(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT));
    }

    public function scopeAcceptedTermsOfUseAfter(Builder $builder, Carbon $datetime): Builder
    {
        return $builder
            ->whereHas(
                'generalSettings',
                fn (Builder $builder2) => $builder2->accepted(PharmacySettingTypes::TERMS_OF_USE)->where('created_at', '>=', $datetime)
            )
            ->whereHas(
                'generalSettings',
                fn (Builder $builder2) => $builder2->accepted(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->where('created_at', '>=', $datetime)
            );
    }

    public function scopeAcceptedTermsOfUseBeforeApomail(Builder $builder): Builder
    {
        $generalSettings = app(ApoMailSettings::class);

        return $builder
            ->whereHas(
                'generalSettings',
                fn (Builder $builder2) => $builder2->accepted(PharmacySettingTypes::TERMS_OF_USE)->where(
                    'created_at',
                    '<',
                    $generalSettings->apomail_due_date_tos
                )
            )
            ->whereHas(
                'generalSettings',
                fn (Builder $builder2) => $builder2->accepted(
                    PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT
                )->where('created_at', '<', $generalSettings->apomail_due_date_tos)
            );
    }

    public function scopeAcceptedTermsOfUseAfterApomail(Builder $builder): Builder
    {
        $generalSettings = app(ApoMailSettings::class);

        return $builder
            ->whereHas(
                'generalSettings',
                fn (Builder $builder2) => $builder2->accepted(PharmacySettingTypes::TERMS_OF_USE)->where(
                    'created_at',
                    '>=',
                    $generalSettings->apomail_due_date_tos
                )
            )
            ->whereHas(
                'generalSettings',
                fn (Builder $builder2) => $builder2->accepted(
                    PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT
                )->where('created_at', '>=', $generalSettings->apomail_due_date_tos)
            );
    }

    public function termsOfUseAccepted(): bool
    {
        return $this->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)?->value === PharmacySettingTypes::TERMS_OF_USE_ACCEPTED;
    }

    public function avvAccepted(): bool
    {
        return $this->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)?->value === PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED;
    }

    public function hasAcceptedTerms(): bool
    {
        if (now()->gt(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline)) {
            return $this->hasAcceptedTermsAfter(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since);
        }

        return $this->termsOfUseAccepted() && $this->avvAccepted();
    }

    public function hasAcceptedTermsBefore(Carbon $datetime): bool
    {
        $termsOfUse = $this->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE);
        $dataContract = $this->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT);

        return $termsOfUse
            && $dataContract
            && $termsOfUse->value === PharmacySettingTypes::TERMS_OF_USE_ACCEPTED
            && $dataContract->value === PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED
            && $termsOfUse->created_at->lessThan($datetime)
            && $dataContract->created_at->lessThan($datetime);
    }

    public function hasAcceptedTermsAfter(Carbon $datetime): bool
    {
        $termsOfUse = $this->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE);
        $dataContract = $this->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT);

        return $termsOfUse
            && $dataContract
            && $termsOfUse->value === PharmacySettingTypes::TERMS_OF_USE_ACCEPTED
            && $dataContract->value === PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED
            && $termsOfUse->created_at->greaterThanOrEqualTo($datetime)
            && $dataContract->created_at->greaterThanOrEqualTo($datetime);
    }

    public function acceptTerms(bool $accepted = true, ?string $type = null): void
    {
        if ($type === null || $type === PharmacySettingTypes::TERMS_OF_USE) {
            $this->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, $accepted ? PharmacySettingTypes::TERMS_OF_USE_ACCEPTED : PharmacySettingTypes::TERMS_OF_USE_DECLINED);
        }

        if ($type === null || $type === PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT) {
            $this->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, $accepted ? PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED : PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_DECLINED);
        }

        if ($type === PharmacySettingTypes::SUBSCRIPTION_AFTER_FRAMEWORK_CONTRACT) {
            $this->setGeneralSetting(PharmacySettingTypes::SUBSCRIPTION_AFTER_FRAMEWORK_CONTRACT, $accepted);
        }
    }

    public function hasAcceptedTermsAfterApomail(): bool
    {
        return $this->acceptedTermsOfUseAfterApomail()->where('id', $this->id)->exists();
    }

    public function isActiveForApi(): bool
    {
        return DigitalRepresentationFeatureAccess::check($this)->canUse();
    }

    public function isAbleToExecuteInfluenzaVaccination(): bool
    {
        /** @var Association $association */
        if (! $association = $this->getAssociation($this)) {
            return true;
        }

        return $association->settings && $association->settings->can_vaccinate;
    }

    public function isAbleToExecuteInfluenzaVaccinationModel(): bool
    {
        /** @var Association $association */
        if (! $association = $this->getAssociation($this)) {
            return false;
        }

        return
            ($association->settings && $association->settings->can_vaccinate_model)
            &&
            $this->settings->vaccinate_status == PharmacyVaccinateStatusEnum::ACTIVE;
    }

    public function isIbmRegistered(): bool
    {
        return $this->is_ibm_registered === IbmRegistrationStatusEnum::REGISTERED;
    }

    /**
     * @deprecated
     */
    public function subscriptionOrders(): MorphMany
    {
        return $this->morphMany(SubscriptionOrder::class, 'orderable');
    }

    public function activeSubscriptionOrders(): MorphMany
    {
        return $this->subscriptionOrders()
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now());
    }

    /**
     * @deprecated
     */
    public function hasSubscribedIfNeeded(): bool
    {
        $associationId = $this->owner()->pharmacyProfile->association_id ?? null;
        if ($associationId !== AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V && $associationId !== null) {
            return true;
        }

        return $this->hasActiveSubscription();
    }

    /**
     * @deprecated
     */
    public function getOrderableItemsForSubscription(Subscription $subscription): self
    {
        return $this;
    }

    public function billingAddress(): BelongsTo
    {
        return $this->belongsTo(BillingAddress::class);
    }

    public function ibmRegistration(): HasOne
    {
        return $this->hasOne(IbmRegistration::class);
    }

    public function guidedDocspaceCreationProcesses(): HasMany
    {
        return $this->hasMany(GuidedDocspaceCreationProcess::class);
    }

    public function subscriptionCancellation(): HasOne
    {
        return $this->hasOne(SubscriptionCancellation::class);
    }

    public function activeCancellation(): ?SubscriptionCancellation
    {
        return $this->subscriptionCancellation()
            ->whereIn('status', [
                SubscriptionCancellationStatusEnum::PENDING,
                SubscriptionCancellationStatusEnum::CONFIRMED,
            ])
            ->first();
    }

    public function docSpaces(): HasMany
    {
        return $this->hasMany(DocSpace::class);
    }

    public function docSpaceGroups(): HasMany
    {
        return $this->hasMany(DocSpaceGroup::class);
    }

    public function getPharmacyIdBase64(): string
    {
        return rtrim(base64_encode(sprintf('%04d', $this->id)), '=');
    }

    public function getSuffixAttribute(): string
    {
        $suffix = $this->getPharmacyIdBase64();

        if (! app()->environment('prod') && ! app()->environment('production') && ! app()->environment('testing')) {
            $suffix .= '-'.now()->timestamp;
        }

        return $suffix;
    }

    public function getUpperShortNameAttribute(): string
    {
        return Str::upper(Str::limit($this->name, 3, ''));
    }

    public function hasPatientChatEnabled(): bool
    {
        return $this->uses_chat && $this->uses_patient_chat;
    }

    public function hasIaEnabled(): bool
    {
        return ! is_null($this->getIntegration(IntegrationTypeEnum::IhreApotheken));
    }

    public function matrixBootstrapDone(): bool
    {
        return $this->users()->where('matrix_bootstrap_done', true)->exists();
    }

    public function advertisingMaterial(): HasOne
    {
        return $this->hasOne(AdvertisingMaterial::class);
    }

    public function iaOrder(): HasOne
    {
        return $this->hasOne(IaOrder::class);
    }

    public function cardLinkOrder(): HasOne
    {
        return $this->hasOne(CardLinkOrder::class);
    }

    public function iaSyncs(): HasMany
    {
        return $this->hasMany(IaSync::class);
    }

    public function latestIaSync(): HasOne
    {
        return $this->hasOne(IaSync::class)->latestOfMany();
    }

    public function setBusinessHours(BusinessHoursDto $dto): void
    {
        $this->businessHours()->delete();

        $this->businessHours()->createManyQuietly($dto->toApRecords());
    }

    public function cardLinkPartnerPharmacy(): HasOne
    {
        return $this->hasOne(CardLinkPartnerPharmacy::class);
    }

    public function hasApoguideAsActiveCardLinkChannel(): bool
    {
        if (! $this->cardLinkOrder) {
            return false;
        }

        return OrderInformationData::fromCardLinkOrder($this->cardLinkOrder)?->activateApoGuideVendor ?? false;
    }

    public function stripeName(): ?string
    {
        return $this->billingAddress?->company;
    }

    public function stripeEmail(): ?string
    {
        return $this->billingAddress?->email;
    }

    public function stripePhone(): ?string
    {
        return $this->billingAddress?->phone;
    }

    /**
     * @return array<string|null>|null
     */
    public function stripeAddress(): ?array
    {
        return [
            'city' => $this->billingAddress?->city,
            'line1' => sprintf('%s %s', $this->billingAddress?->street, $this->billingAddress?->house_number),
            'line2' => $this->billingAddress?->optional_address_line,
            'postal_code' => $this->billingAddress?->postal_code,
            'country' => 'DE',
        ];
    }

    /**
     * @return string[]
     */
    public function stripePreferredLocales(): array
    {
        return ['de'];
    }

    /** @return string[] */
    public function taxRates(): array
    {
        if (! $this->has_vat) {
            return [];
        }

        return [app(TaxStripeSetting::class)->ust_tax_id];
    }

    public function sepaExists(): bool
    {
        return $this->paymentMethods('sepa_debit')->isNotEmpty();
    }

    public function createAsStripeCustomer(array $options = [])
    {
        $options['invoice_settings'] = [
            'rendering_options' => [
                'template' => app(TemplateStripeSetting::class)->template_id,
            ],
        ];

        return $this->createAsStripeCustomerTrait($options);
    }

    public function calculateNextQuarterEnd(): Carbon
    {
        // Ende der Mindestvertragslaufzeit
        $minTermEnd = $this->subscription()->created_at->copy()->addMonthsNoOverflow(3);

        // Fristgerecht spätestens 1 Monat vorher kündigen: heute + 1 Monat
        $noticeThreshold = now()->copy()->addMonthsNoOverflow(1);

        // Wir brauchen den spätesten Termin aus beiden
        $threshold = $minTermEnd->gt($noticeThreshold) ? $minTermEnd : $noticeThreshold;

        return $threshold->endOfQuarter();
    }
}
