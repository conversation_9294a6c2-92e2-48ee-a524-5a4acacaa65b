<?php

namespace App\Domains\Subscription\Application\StripeProducts;

use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
use App\Domains\Payment\Domain\Data\StripePriceData;
use App\Domains\Payment\Domain\Data\StripePublicRepresentationData;
use App\Domains\Subscription\Application\AssociationFrameworkContracts\BaseAssociationFrameworkContract;
use App\Domains\Subscription\Application\AssociationFrameworkContracts\PlusAssociationFrameworkContract;
use App\Domains\Subscription\Application\Settings\Products\StripeProductSetting;
use App\Domains\Subscription\Domain\Actions\GetPriceFromStripeAction;
use App\Domains\Subscription\Domain\Data\OneTimePriceData;
use App\Pharmacy;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Laravel\Cashier\Subscription;

abstract class StripeProduct
{
    public static bool $isProrated = true;

    abstract public function getSettings(): StripeProductSetting;

    public function getStripeProductId(): string
    {
        // assert(is_string($id = $this->getSettings()->product_id));

        return $this->getSettings()->product_id;
    }

    public function getRecurringStripePrice(Pharmacy $pharmacy): StripePriceData
    {
        assert(is_string($id = $this->getSettings()->recurring_price_id));

        return new StripePriceData($id, app(GetPriceFromStripeAction::class)->execute($id));
    }

    public function getOneTimeStripePrice(Pharmacy $pharmacy): StripePriceData
    {
        assert(is_string($id = $this->getSettings()->one_time_price_id));

        return new StripePriceData($id, app(GetPriceFromStripeAction::class)->execute($id));
    }

    abstract public function getPublicRepresentationData(Pharmacy $pharmacy): StripePublicRepresentationData;

    public function isRequired(Pharmacy $pharmacy): bool
    {
        return $pharmacy->canUseProduct(static::class, true);
    }

    public function uniqueIdentifier(): string
    {
        return class_basename($this);
    }

    /** @return OneTimePriceData[] */
    public function getOneTimePriceArrayForProration(Pharmacy $pharmacy, Carbon $recurringSubscriptionStartsAt, ?Carbon $currentDate = null): array
    {
        return [];
    }

    public static function make(): static
    {
        return app(static::class);
    }

    /**
     * @param  Builder<Pharmacy>  $builder
     * @return Builder<Pharmacy>
     */
    public static function scopePharmacyBuilder(Builder $builder): Builder
    {
        // kulanz
        if (collect(Pharmacy::getProductsCurrentlyUsableWithoutSubscription())->contains(static::class)) {
            return $builder;
        }

        $builder->where(function ($builder) {
            $builder->when(collect(app(BaseAssociationFrameworkContract::class)->getIncludedProducts())->contains(static::class), function (Builder $builder) {
                return $builder->orWhereHas('users.pharmacyProfile.association.currentAssociationFrameworkContractHistory', function (Builder $builder) {
                    $builder->where('contract', AssociationFrameworkContractEnum::BaseAssociationFrameworkContract);
                });
            });

            $builder->when(app(PlusAssociationFrameworkContract::class)->getIncludedProducts(), function (Builder $builder) {
                return $builder->orWhereHas('users.pharmacyProfile.association.currentAssociationFrameworkContractHistory', function (Builder $builder) {
                    $builder->where('contract', AssociationFrameworkContractEnum::PlusAssociationFrameworkContract);
                });
            });

            // stripe subscription
            $builder->orWhereHas('subscriptions', function (Builder $builder) {
                (new Subscription)->scopeActive($builder);

                $builder->whereHas('items', function (Builder $builder) {
                    $builder->where('stripe_product', static::make()->getStripeProductId());
                });
            });
        });

        return $builder;

    }
}
