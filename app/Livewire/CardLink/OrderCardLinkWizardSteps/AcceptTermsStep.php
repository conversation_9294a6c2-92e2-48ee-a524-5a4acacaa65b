<?php

namespace App\Livewire\CardLink\OrderCardLinkWizardSteps;

use App\Data\CardLink\OrderInformationData;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Exceptions\OrderFeatureBlockedException;
use App\Livewire\Wizard\WizardStep;
use App\Pharmacy;
use App\Processes\OrderCardLink;
use App\Processes\Payloads\OrderCardLinkPayload;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;
use App\User;
use Throwable;

class AcceptTermsStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public bool $acceptedTerms = false;

    public bool $acceptedPrivacy = false;

    public bool $acceptedPricing = false;

    public ?string $nextStep = ActivateApoGuideStep::class;

    protected function title(): string
    {
        return 'CardLink bestellen';
    }

    protected function subtitle(): ?string
    {
        return 'Sie bestellen für die Apotheke: '.currentPharmacy()?->name;
    }

    protected function prevStep(): ?string
    {
        return ChoosePackageStep::class;
    }

    protected function nextStepLabel(): string
    {
        return 'Kostenpflichtig bestellen';
    }

    /**
     * @return array<string, array<string>>
     */
    public function rules(): array
    {
        return [
            'acceptedTerms' => ['required', 'accepted'],
            'acceptedPrivacy' => ['required', 'accepted'],
            'acceptedPricing' => ['required', 'accepted'],
        ];
    }

    /**
     * @return array<string, array<string>>
     */
    public function messages(): array
    {
        return [
            'acceptedTerms' => [
                'accepted' => 'Sie müssen den Nutzungsbedingungen zustimmen um CardLink zu bestellen.',
            ],
            'acceptedPrivacy' => [
                'accepted' => 'Sie müssen den Datenschutzbestimmungen zustimmen um CardLink zu bestellen.',
            ],
            'acceptedPricing' => [
                'accepted' => 'Sie müssen bestätigen, dass Sie die Preisinformationen gelesen haben.',
            ],
        ];
    }

    public function submit(): void
    {
        $this->validate();

        try {
            $package = data_get($this->getStateFromStep(ChoosePackageStep::class), 'selectedPackage');
            assert(is_string($package));
            $package = CardLinkPackageEnum::from($package);

            $pharmacy = currentPharmacy();
            assert($pharmacy instanceof Pharmacy);

            $owner = $pharmacy->owner();
            assert($owner instanceof User);

            $user = auth()->user();
            assert($user instanceof User);

            if ($pharmacy->activeCancellation()) {
                throw new OrderFeatureBlockedException;
            }

            $payload = new OrderCardLinkPayload(
                $pharmacy,
                $user,
                $owner,
                OrderInformationData::from([
                    'package' => $package,
                ])
            );
            $process = new OrderCardLink;
            $process->run($payload);
        } catch (Throwable $exception) {
            report($exception);

            $this->nextStep = FailedStep::class;
        }

        $this->next();
    }

    public function sessionPrefix(): ?string
    {
        return 'card-link';
    }
}
