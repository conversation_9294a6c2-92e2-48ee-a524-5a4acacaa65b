<?php

namespace App\Livewire\Ia\AuthFlow;

use App\Actions\IhreApotheken\AcceptConsentAction;
use App\Attributes\Persist;
use App\Enums\Ia\IaOrderStatusEnum;
use App\Exceptions\OrderFeatureBlockedException;
use App\Helper\IaHelper;
use App\IaOrder;
use App\Livewire\Wizard\WizardStep;
use App\Mail\IA\OnlineShopOrderedToCustomer;
use App\Mail\IA\OnlineShopOrderedToIA;
use App\Pharmacy;
use App\Rules\AtleastOnePharmacySelected;
use App\Rules\Ownership;
use App\Traits\InteractsWithSession;
use App\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\Rule;

class Order extends WizardStep
{
    use InteractsWithSession;

    public string $title = 'Online-Shop bestellen';

    public ?string $subtitle = 'Bitte wählen Sie hier die Apotheke(n) aus, für die Sie den Online-Shop bestellen wollen.';

    public string $nextStepLabel = 'Jetzt kostenpflichtig bestellen';

    public ?string $nextStep = OrderConfirmation::class;

    public ?string $prevStep = BeforeWeStart::class;

    /** @var array<array<string, string>> */
    public array $pharmacies = [];

    #[Persist]
    public bool $termsAccepted = false;

    #[Persist]
    public bool $privacyPolicyAccepted = false;

    public function mount(): void
    {
        $user = user();

        $user || abort(403);

        /** @var array<array<string, string>> $pharmacies */
        $pharmacies = $user->pharmacies()
            ->with('homeAddress')
            ->whereDoesntHave('iaOrder')
            ->get()
            ->filter(function (Pharmacy $pharmacy) {
                return ! $pharmacy->hasIaEnabled();
            })
            ->map(function (Pharmacy $pharmacy) {
                return [
                    'id' => $pharmacy->id,
                    'name' => $pharmacy->name,
                    'address' => sprintf(
                        '%s %s, %s %s',
                        $pharmacy->homeAddress?->street,
                        $pharmacy->homeAddress?->house_number,
                        $pharmacy->homeAddress?->postcode,
                        $pharmacy->homeAddress?->city,
                    ),
                    'selected' => false,
                ];
            })->toArray();

        $this->pharmacies = $pharmacies;
    }

    /**
     * @return array<string, array<string|ValidationRule>>
     */
    public function rules(): array
    {
        /** @var User $user */
        $user = user();

        return [
            'pharmacies' => ['required', 'array', new AtleastOnePharmacySelected('selected')],
            'pharmacies.*.id' => ['required', 'int', Rule::exists('pharmacies', 'id'), new Ownership($user)],
            'pharmacies.*.selected' => ['required', 'boolean'],
            'termsAccepted' => ['required', 'accepted'],
            'privacyPolicyAccepted' => ['required', 'accepted'],
        ];
    }

    /**
     * @return string[]
     */
    public function messages(): array
    {
        return [
            'termsAccepted.accepted' => 'Die Nutzungsbedingungen müssen akzeptiert werden.',
            'privacyPolicyAccepted.accepted' => 'Die Datenschutzbestimmungen müssen akzeptiert werden.',
        ];
    }

    public function submit(): void
    {
        $this->resetErrorBag();
        $this->resetValidation();

        $this->validate();

        /** @var ?User $user */
        $user = user();

        if (! $user) {
            abort(403);
        }

        $orderedPharmacies = collect($this->pharmacies)
            ->filter(fn (array $pharmacy) => $pharmacy['selected']); // @phpstan-ignore-line

        foreach ($orderedPharmacies as $pharmacy) {

            $pharmacyEloquent = Pharmacy::find($pharmacy['id']);

            if ($pharmacyEloquent?->activeCancellation()) {
                throw new OrderFeatureBlockedException;
            }

            /** @var int $pharmacyId */
            $pharmacyId = $pharmacy['id'];

            /** @var array<string, mixed> $pharmacy */
            IaOrder::create([
                'pharmacy_id' => $pharmacyId,
                'user_id' => $user->id,
                'status' => IaOrderStatusEnum::TransmittedToIa,
            ]);

            if (config('services.consent.use')) {
                (new AcceptConsentAction((string) $pharmacyId, false))->execute();
            }
        }

        $pharmacies = $user->pharmacies()->whereIn('id', $orderedPharmacies->pluck('id'))->get();

        Mail::to($user)->queue(new OnlineShopOrderedToCustomer($user, $pharmacies, now()));
        Mail::to('<EMAIL>')->queue(new OnlineShopOrderedToIA($user, $pharmacies, now()));

        $this->next();
    }

    protected function sessionPrefix(): ?string
    {
        return IaHelper::wizardSessionPrefix();
    }

    public function render(): View
    {
        return view('livewire.ia.auth-flow.order');
    }
}
