<?php

namespace App;

use App\Enums\SubscriptionCancellationStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property \Illuminate\Support\Carbon $requested_at
 * @property SubscriptionCancellationStatusEnum $status
 * @property \Illuminate\Support\Carbon $effective_date
 * @property \Illuminate\Support\Carbon|null $confirmed_at
 * @property \Illuminate\Support\Carbon|null $revoked_at
 * @property \Illuminate\Support\Carbon|null $executed_at
 * @property string|null $reason
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Pharmacy $pharmacy
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereConfirmedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereEffectiveDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereExecutedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereRequestedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereRequestedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereRevokedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereRevokedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereUpdatedAt($value)
 *
 * @property string|null $requestable_type
 * @property int|null $requestable_id
 * @property string|null $revokable_type
 * @property int|null $revokable_id
 * @property-read Model|\Eloquent|null $requestable
 * @property-read Model|\Eloquent|null $revokable
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereRequestableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereRequestableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereRevokableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionCancellation whereRevokableType($value)
 *
 * @mixin \Eloquent
 */
class SubscriptionCancellation extends Model
{
    use HasFactory;

    protected $fillable = [
        'pharmacy_id',
        'requestable_type',
        'requestable_id',
        'requested_at',
        'status',
        'effective_date',
        'confirmed_at',
        'revokable_type',
        'revokable_id',
        'revoked_at',
        'executed_at',
        'reason',
        'metadata',
    ];

    protected $casts = [
        'requested_at' => 'datetime',
        'status' => SubscriptionCancellationStatusEnum::class,
        'effective_date' => 'datetime',
        'confirmed_at' => 'datetime',
        'revoked_at' => 'datetime',
        'executed_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function requestable(): MorphTo
    {
        return $this->morphTo();
    }

    public function revokable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Prüft ob die Kündigung noch widerrufen werden kann
     */
    public function canBeRevoked(): bool
    {
        return in_array($this->status, [
            SubscriptionCancellationStatusEnum::PENDING,
            SubscriptionCancellationStatusEnum::CONFIRMED,
        ]) && $this->effective_date->isFuture();
    }

    /**
     * Prüft ob die Kündigung wirksam ist
     */
    public function isEffective(): bool
    {
        return $this->status === SubscriptionCancellationStatusEnum::EXECUTED
            || ($this->status === SubscriptionCancellationStatusEnum::CONFIRMED && $this->effective_date->isPast());
    }
}
