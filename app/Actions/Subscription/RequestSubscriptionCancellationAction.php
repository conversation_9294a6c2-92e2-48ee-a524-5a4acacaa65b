<?php

namespace App\Actions\Subscription;

use App\Domains\Subscription\Application\StripeProducts\AddOns\CardLinkStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\AddOns\IAStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\AddOns\KimProduct;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\Mail\SubscriptionCancellationRequestedMail;
use App\Pharmacy;
use App\Staff;
use App\SubscriptionCancellation;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class RequestSubscriptionCancellationAction
{
    public function execute(Pharmacy $pharmacy, User|Staff $user, ?string $reason = null): SubscriptionCancellation
    {
        return DB::transaction(function () use ($pharmacy, $user, $reason) {

            $this->validateCancellationRequest($pharmacy);

            $effectiveDate = $pharmacy->calculateNextQuarterEnd();

            if (! $this->meetsMinimumRuntime($pharmacy, $effectiveDate)) {
                throw new SubscriptionCancellationException(
                    'Die Mindestlaufzeit von 3 Monaten und/oder Kündigungsfrist von einem Monat wurde noch nicht erreicht.'
                );
            }

            if ($this->hasActiveBookableFeatures($pharmacy)) {
                throw new SubscriptionCancellationException(
                    'Es sind noch buchbare Features aktiv. Bitte kündigen Sie diese zuerst.'
                );
            }

            $subscriptionCancellation = SubscriptionCancellation::create([
                'pharmacy_id' => $pharmacy->id,
                'status' => SubscriptionCancellationStatusEnum::CONFIRMED,
                'requested_at' => now(),
                'confirmed_at' => now(),
                'effective_date' => $effectiveDate,
                'reason' => $reason,
            ]);

            $subscriptionCancellation->requestable()->associate($user)->save();

            $emails = $pharmacy
                ->ownersAndSubowners()
                ->pluck('email')
                ->unique()
                ->toArray();

            Mail::to($emails)->queue(new SubscriptionCancellationRequestedMail($pharmacy, $subscriptionCancellation));

            return $subscriptionCancellation;
        });
    }

    private function validateCancellationRequest(Pharmacy $pharmacy): void
    {
        if ($pharmacy->subscriptionCancellation()->whereIn('status', [
            SubscriptionCancellationStatusEnum::PENDING,
            SubscriptionCancellationStatusEnum::CONFIRMED,
        ])->exists()) {
            throw new SubscriptionCancellationException(
                'Es existiert bereits eine aktive Kündigung für diese Apotheke.'
            );
        }

        if (! $pharmacy->isSubscribedToProduct(BaseStripeProduct::class)) {
            throw new SubscriptionCancellationException(
                'Es ist keine Basismitgliedschaft vorhanden, die gekündigt werden kann.'
            );
        }
    }

    private function meetsMinimumRuntime(Pharmacy $pharmacy, Carbon $effectiveDate): bool
    {
        $subscription = $pharmacy->subscription();
        assert($subscription instanceof \Laravel\Cashier\Subscription);

        $subscriptionStart = $subscription->created_at;

        if (! $subscriptionStart) {
            return false;
        }

        if (! $subscriptionStart->addMonths(3)->lessThan($effectiveDate)) {
            return false;
        }

        if (! Carbon::now()->addMonths(1)->lessThan($effectiveDate)) {
            return false;
        }

        return true;
    }

    private function hasActiveBookableFeatures(Pharmacy $pharmacy): bool
    {
        $bookableProducts = [
            KimProduct::class,
            CardLinkStripeProduct::class,
            IAStripeProduct::class,
        ];

        foreach ($bookableProducts as $product) {
            if ($pharmacy->isSubscribedToProduct($product)) {
                return true;
            }
        }

        return false;
    }
}
