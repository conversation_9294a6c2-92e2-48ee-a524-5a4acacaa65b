<?php

namespace App\Actions\Subscription;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\Pharmacy;
use App\Staff;
use App\SubscriptionCancellation;
use App\User;
use Illuminate\Support\Facades\DB;

class RevokeSubscriptionCancellationAction
{
    public function execute(Pharmacy $pharmacy, User|Staff $user, ?string $reason = null): SubscriptionCancellation
    {
        return DB::transaction(function () use ($pharmacy, $user, $reason) {
            $cancellation = $pharmacy->activeCancellation();

            if (! $cancellation) {
                throw new SubscriptionCancellationException(
                    'Es wurde keine aktive Kündigung gefunden, die widerrufen werden kann.'
                );
            }

            if (! $cancellation->canBeRevoked()) {
                throw new SubscriptionCancellationException(
                    'Diese Kündigung kann nicht mehr widerrufen werden.'
                );
            }

            $cancellation->update([
                'status' => SubscriptionCancellationStatusEnum::REVOKED,
                'revoked_at' => now(),
                'reason' => $cancellation.($reason ? ' / Widerruf: '.$reason : ''),
            ]);

            $cancellation->revokable()->associate($user)->save();

            return $cancellation;
        });
    }
}
