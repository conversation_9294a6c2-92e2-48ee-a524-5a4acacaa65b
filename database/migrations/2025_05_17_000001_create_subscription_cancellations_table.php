<?php

use App\Enums\SubscriptionCancellationStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subscription_cancellations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pharmacy_id')->constrained()->onDelete('cascade');
            $table->nullableMorphs('requestable');
            $table->timestamp('requested_at');
            $table->string('status')->default(SubscriptionCancellationStatusEnum::PENDING->value);
            $table->timestamp('effective_date');
            $table->timestamp('confirmed_at')->nullable();
            $table->nullableMorphs('revokable');
            $table->timestamp('revoked_at')->nullable();
            $table->timestamp('executed_at')->nullable();
            $table->text('reason')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subscription_cancellations');
    }
};
