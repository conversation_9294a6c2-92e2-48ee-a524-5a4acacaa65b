<?php

namespace Database\Factories;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Pharmacy;
use App\SubscriptionCancellation;
use App\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionCancellationFactory extends Factory
{
    protected $model = SubscriptionCancellation::class;

    public function definition(): array
    {
        return [
            'pharmacy_id' => Pharmacy::factory(),
            'requested_at' => now(),
            'status' => SubscriptionCancellationStatusEnum::PENDING,
            'effective_date' => now()->addMonth(),
            'reason' => $this->faker->optional()->sentence(),
        ];
    }

    public function pending(): static
    {
        return $this->state(fn () => [
            'status' => SubscriptionCancellationStatusEnum::PENDING,
        ]);
    }

    public function confirmed(): static
    {
        return $this->state(fn () => [
            'status' => SubscriptionCancellationStatusEnum::CONFIRMED,
            'confirmed_at' => now(),
        ]);
    }

    public function revoked(): static
    {
        return $this->state(fn () => [
            'status' => SubscriptionCancellationStatusEnum::REVOKED,
            'revoked_at' => now(),
        ]);
    }

    public function executed(): static
    {
        return $this->state(fn () => [
            'status' => SubscriptionCancellationStatusEnum::EXECUTED,
            'executed_at' => now(),
        ]);
    }
}
